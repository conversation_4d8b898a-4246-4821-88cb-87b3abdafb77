import React, { useState } from "react";
import { View, Text, StyleSheet, Platform, Pressable } from "react-native";
import {
  runSharedState,
  sharedState,
} from "@src/shared-state/shared-state";
import { cachedDataInfo, DataSyncPerformance, dataSyncProfiling, profileDataSync, resetCachedDataInfo, updateLicenseeCachedDataInfo, updateVesselCachedDataInfo } from "@src/shared-state/DataSyncSystem/cachedDataInfo";
import { licenseeCollectionsToDataSync, LicenseeDataSyncCollection, vesselCollectionsToDataSync, VesselDataSyncCollection } from "@src/shared-state/DataSyncSystem/dataSyncTasks";
import { colors } from "@src/theme/globalStyle";
import { formatDatetime } from "@src/lib/datesAndTime";
import { renderVesselName } from "@src/shared-state/Core/vessels";

const DebugDataSyncTab: React.FC = () => {
  const isDataSyncActive = sharedState.isDataSyncActive.use();
  const dataSyncStatus = sharedState.dataSyncStatus.use();
  const licenceeSettings = sharedState.licenseeSettings.use();
  const vesselIds = sharedState.vesselIds.use();
  const [valuesToShow, setValuesToShow] = useState<any>({});
  const [drawCount, setDrawCount] = useState(0);

  const toggleItem = (id: string) => {
    setValuesToShow((current: any) => {
      const newValuesToShow = {...current};
      if (current[id]) {
        delete newValuesToShow[id];
      } else {
        newValuesToShow[id] = true;
      }
      return newValuesToShow;
    });
  };

  const getLatestFrom = Date.now() - (365 * 24 * 60 * 60 * 1000);

  const getForLicenseeCollection = (collection: string, isGetAll: boolean) => {
    updateLicenseeCachedDataInfo(
      collection as LicenseeDataSyncCollection,
      isGetAll ? undefined : getLatestFrom
    );
    runSharedState('whenLicenseeTouched');
    setDrawCount((n) => n+1);
  };

  const getForVesselCollection = (vesselId: string, collection: string, isGetAll: boolean) => {
    updateVesselCachedDataInfo(
      vesselId,
      collection as VesselDataSyncCollection,
      isGetAll ? undefined : getLatestFrom
    );
    runSharedState('whenVesselTouched');
    setDrawCount((n) => n+1);
  };

  const getLatestForAll = () => {
    licenseeCollectionsToDataSync.forEach((collection) => {
        updateLicenseeCachedDataInfo(
            collection,
            getLatestFrom
        );
    });
    runSharedState('whenLicenseeTouched');
    vesselIds?.forEach((vesselId) => {
      vesselCollectionsToDataSync.forEach((collection) => {
        updateVesselCachedDataInfo(
          vesselId,
          collection,
          getLatestFrom
        );
      });
    });
    runSharedState('whenVesselTouched');
    setDrawCount((n) => n+1);
  };

  if (!isDataSyncActive) {
    return (
      <View style={{ paddingLeft: 8, paddingTop: 4, paddingRight: 8 }}>
        <Text>DataSync is not active.</Text>
        {!licenceeSettings?.hasOffline &&
          <Text>DataSync cannot operate while <Text style={{ fontWeight: "bold" }}>licenseeSettings.hasOffline</Text> is not enabled</Text>
        }
        {Platform.OS === "web" &&
          <Text>DataSync is only active for native apps. You can set <Text style={{ fontWeight: "bold" }}>isWebPretendingToBeNative=true</Text> to see it working on web (just don&apos;t forget to set it back!)</Text>
        }
      </View>
    );
  }

  if (!profileDataSync) {
    return <View><Text>Data Sync profiling is off because this is in the production environment.</Text></View>;
  }

  return (
    <>
      <View style={[style.row]}>
        <View style={{ paddingTop: 3 }}>
          <Text>
            Total Tasks: <Text style={style.bold}>{dataSyncStatus?.totalTasks ?? 0}</Text>, Tasks Left: <Text style={style.bold}>{dataSyncStatus?.tasksLeft ?? 0}</Text>
          </Text>
        </View>
        <View style={{ paddingLeft: 12 }}>
          <Pressable onPress={() => resetCachedDataInfo()}>
            <Text style={{ color: colors.primary }}>getAll</Text>
          </Pressable>
        </View>
        <View style={{ paddingLeft: 8 }}>
          <Pressable onPress={() => getLatestForAll()}>
            <Text style={{ color: colors.primary }}>getLatest</Text>
          </Pressable>
        </View>
      </View>
      <View>
        <Text style={style.head}>Cached Licensee Data</Text>
      </View>
      <View style={{ width: 815 }}>
        <View style={style.row}>
          {columns.map((column) => {
            return (
              <View key={column.name} style={column.style}>
                <Text style={[column.textStyle as any, { fontSize: 11 }]}>{column.name}</Text>
              </View>
            );
          })}
          <View></View>
          <View></View>
        </View>

        {Object.keys(cachedDataInfo.licensee).sort().map((key) => {
          const when = cachedDataInfo.licensee?.[key as keyof typeof cachedDataInfo.licensee];
          const perfs = dataSyncProfiling.licensee[key as LicenseeDataSyncCollection];
          const stats = compileStats(perfs);
          return (
            <React.Fragment key={key}>
              <Pressable
                style={[style.row, style.card, { backgroundColor: stats?.hasErrors ? "#ff9999" : "#ffffff" }]}
                onPress={(e) => toggleItem(key)}
              >
                {columns.map((column, index) => {
                  let item = stats as any;
                  if (index === 0) {
                    item = key;
                  } else if (index === 1) {
                    item = when;
                  }
                  return (
                    <View key={column.name} style={column.style}>
                      <Text style={[{fontSize: 12}, column.textStyle as any]}>
                        {column.value(item)}
                      </Text>
                    </View>
                  );
                })}
                <Pressable style={{ flexBasis: 40 }} onPress={() => getForLicenseeCollection(key, true)}>
                  <Text style={{ fontSize: 12, color: colors.primary }}>getAll</Text>
                </Pressable>
                <Pressable style={{ flexBasis: 60 }} onPress={() => getForLicenseeCollection(key, false)}>
                  <Text style={{ fontSize: 12, color: colors.primary }}>getLatest</Text>
                </Pressable>
              </Pressable>
              {valuesToShow[key] && 
                <Pressable onPress={() => toggleItem(key)}>
                  {renderStats(perfs)}
                </Pressable>
              }
            </React.Fragment>
          );
        })}

        {Object.keys(cachedDataInfo.vessels).map((vesselId) => {
          return (
            <React.Fragment key={vesselId}>
              <View>
                <Text style={style.head}>{renderVesselName(vesselId)} - {vesselId}</Text>
              </View>
              {Object.keys(cachedDataInfo.vessels[vesselId]).sort().map((collection) => {
                const when = cachedDataInfo.vessels[vesselId][collection as keyof typeof cachedDataInfo.vessels[string]];
                const perfs = dataSyncProfiling.vessels[vesselId]?.[collection as VesselDataSyncCollection];
                const stats = compileStats(dataSyncProfiling.vessels[vesselId]?.[collection as VesselDataSyncCollection]);

                return (
                  <React.Fragment key={`${vesselId}${collection}`}>
                    <Pressable
                      style={[style.row, style.card, { backgroundColor: stats?.hasErrors ? "#ff9999" : "#ffffff" }]}
                      onPress={(e) => toggleItem(`${vesselId}${collection}`)}
                    >
                      {columns.map((column, index) => {
                        let item = stats as any;
                        if (index === 0) {
                          item = collection;
                        } else if (index === 1) {
                          item = when;
                        }
                        return (
                          <View key={column.name} style={column.style}>
                            <Text style={[{fontSize: 12}, column.textStyle as any]}>
                              {column.value(item)}
                            </Text>
                          </View>
                        );
                      })}
                      <Pressable style={{ flexBasis: 40 }} onPress={() => getForVesselCollection(vesselId, collection, true)}>
                        <Text style={{ fontSize: 12, color: colors.primary }}>getAll</Text>
                      </Pressable>
                      <Pressable style={{ flexBasis: 60 }} onPress={() => getForVesselCollection(vesselId, collection, false)}>
                        <Text style={{ fontSize: 12, color: colors.primary }}>getLatest</Text>
                      </Pressable>
                    </Pressable>
                    {valuesToShow[`${vesselId}${collection}`] && 
                      <Pressable onPress={() => toggleItem(`${vesselId}${collection}`)}>
                        {renderStats(perfs)}
                      </Pressable>
                    }
                  </React.Fragment>
                );
              })}
            </React.Fragment>
          );
      })}
      </View>
    </>
  );
};

const columns = [
  {
    name: "Collection",
    style: { flexBasis: 230 },
    textStyle: { textAlign: "left", fontSize: 14 },
    value: (item: any) => item,
  },
  {
    name: "Last Touched",
    style: { flexBasis: 120 },
    textStyle: {textAlign: "left"},
    value: (item: any) => item ? formatDatetime(item, " - ") : "-",
  },
  {
    name: "All Ops",
    style: { flexBasis: 45, paddingLeft: 4 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item?.allOps ?? '-',
  },
  {
    name: "Lastest Ops",
    style: { flexBasis: 45, paddingLeft: 4 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item?.latestOps ?? '-',
  },
  {
    name: "Docs",
    style: { flexBasis: 65, paddingLeft: 4 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item?.docs ?? '-',
  },
  {
    name: "Load (ms)",
    style: { flexBasis: 65, paddingLeft: 4 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item?.loadTime !== undefined ? item.loadTime.toFixed(1) : '-',
  },
    {
    name: "Process (ms)",
    style: { flexBasis: 65, paddingLeft: 4 },
    textStyle: { textAlign: "right" },
    value: (item: any) => item?.processTime !== undefined ? item.processTime.toFixed(1) : '-',
  }
];

const compileStats = (perfs?: DataSyncPerformance[]) => {
  if (!perfs) {
    return undefined;
  }
  const stats = {
    docs: 0,
    allOps: 0,
    latestOps: 0,
    loadTime: 0,
    processTime: 0,
    hasErrors: false,
  };
  perfs.forEach((perf) => {
    if (perf[1] === 'getAll') stats.allOps++;
    if (perf[1] === 'getLatest') stats.latestOps++;
    stats.docs += perf[2];
    stats.loadTime += perf[3];
    stats.processTime += perf[4];
    if (perf[5]) {
      stats.hasErrors = true;
    }
  });
  return stats;
};

const renderTimeTaken = (millisFloat: number) => {
  return millisFloat.toFixed(1);
};

const renderStats = (perfs: DataSyncPerformance[] | undefined) => {
  let previousWhen = perfs?.[0][0] ?? 0;
  return (
    <>
      <View style={{ flexDirection: "row" }}>
        <View style={{ width: 180 }}><Text style={{ textAlign: "right" }}>When</Text></View>
        <View style={{ width: 70 }}><Text style={{ textAlign: "right", paddingLeft: 4 }}>Operation</Text></View>
        <View style={{ width: 60 }}><Text style={{ textAlign: "right" }}>Docs</Text></View>
        <View style={{ width: 100 }}><Text style={{ textAlign: "right" }}>Load Time</Text></View>
        <View style={{ width: 100 }}><Text style={{ textAlign: "right" }}>Process Time</Text></View>
        <View></View>
      </View>
      {perfs?.map((perf, index) => {
        const diff = Number(perf[0]) - previousWhen;
        previousWhen = Number(perf[0]);
        return (
          <View key={perf[0]} style={{ flexDirection: "row" }}>
            <View style={{ width: 180 }}>
              <Text style={{ textAlign: "right" }}>
                {index > 0 && `+${renderTimeTaken(Number(diff))}`}
              </Text>
            </View>
            {perf[5] ? (
              <View style={{ paddingLeft: 12 }}>
                <Text>{perf[5]?.join(', ')}</Text>
              </View>
            ) : (
              <>
                <View style={{ width: 70 }}><Text style={{ textAlign: "right" }}>{perf[1]}</Text></View>
                <View style={{ width: 60 }}><Text style={{ textAlign: "right" }}>{perf[2]}</Text></View>
                <View style={{ width: 100 }}><Text style={{ textAlign: "right" }}>{renderTimeTaken(perf[3])}</Text></View>
                <View style={{ width: 100 }}><Text style={{ textAlign: "right" }}>{renderTimeTaken(perf[4])}</Text></View>
              </>
            )}
          </View>
        );
      })}
      <View style={{ height: 12 }}></View>
    </>
  );
};

const style = StyleSheet.create({
  head: {
    paddingTop: 8,
    fontWeight: "bold",
    paddingBottom: 4,
  },
  bold: {
    fontWeight: "bold",
  },
  group: {
    fontSize: 16
  },
  row: {
    flexDirection: "row",
    columnGap: 8,
    padding: 8,
    maxWidth: 1000,
  },
  card: {
    backgroundColor: "#ffffff",
    borderRadius: 10,
    marginBottom: 4,
  },
  value: {
    paddingLeft: 8,
    paddingBottom: 16,
    paddingRight: 8,
    fontSize: 12,
  }
});

export default DebugDataSyncTab;
