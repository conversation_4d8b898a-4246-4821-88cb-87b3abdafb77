import { Duration } from "luxon";
import React, { useEffect, useState } from "react";
import { Platform, TextInput, TextStyle, View, ViewStyle } from "react-native";
import { colors } from "@src/theme/colors";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { fontFamily } from "@src/theme/typography";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { ErrorText } from "@src/components/_atoms/_inputs/ErrorText/ErrorText";

interface SeaDurationInputProps {
  value?: Duration;
  onChange: (value: Duration) => void;
  label?: string;
  hasError?: boolean;
  errorText?: string;
  style?: ViewStyle;
}

export const SeaDurationInput: React.FC<SeaDurationInputProps> = ({
  value,
  onChange,
  label,
  hasError = false,
  errorText,
  style,
}) => {
  const { styles } = useStyles(styleSheet);

  useEffect(() => {
    if (!value) return;
    // Set initial values if provided
    const { hours, minutes } = value.shiftTo("hours", "minutes").toObject();
    setHours(hours?.toString() ?? "");
    setMinutes(minutes?.toString() ?? "");
  }, []);

  const [hours, setHours] = useState("");
  const [minutes, setMinutes] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const onFocus = () => {
    setIsFocused(true);
    if (!hours && !minutes) {
      setHours("00");
      setMinutes("00");
    }
  };

  const onBlur = () => {
    setIsFocused(false);
    if (hours === "00" && minutes === "00") {
      setHours("");
      setMinutes("");
    }
  };

  const handleChange = (hours: string, minutes: string) => {
    const hoursNumeric = parseInt(hours);
    const minsNumeric = parseInt(minutes);

    console.log("Duration changed", {
      hours,
      minutes,
      hoursNumeric,
      minsNumeric,
    });
    if (isNaN(hoursNumeric) || isNaN(minsNumeric)) {
      // TODO - Add validation error
      return;
    }

    const duration = Duration.fromObject({
      hours: hoursNumeric,
      minutes: minsNumeric,
    });

    onChange(duration);
  };

  const platformOverrides = Platform.select({
    web: { outline: "none" } as TextStyle,
  });

  return (
    <View style={[styles.container, style]}>
      <SeaTypography variant={"label"}>{label?.toUpperCase()}</SeaTypography>

      <View
        style={[styles.inputContainer, hasError && styles.inputContainerError]}
      >
        {/* Hours */}
        <SeaStack
          direction={"row"}
          justify={"start"}
          align={"center"}
          padding={0}
          gap={4}
          style={styles.fieldContainer}
        >
          <TextInput
            value={hours}
            keyboardType={"number-pad"}
            onChangeText={(hours) => {
              setHours(hours);
              handleChange(hours, minutes);
            }}
            selectTextOnFocus={true}
            placeholder={isFocused ? undefined : label}
            placeholderTextColor={colors.text.placeholder}
            onFocus={onFocus}
            onBlur={onBlur}
            style={[
              styles.textInputStyle,
              platformOverrides,
              {
                width: !isFocused && !hours ? "100%" : 30,
              },
            ]}
          />
          {(isFocused || !!hours) && (
            <SeaTypography
              variant={"input"}
              containerStyle={{
                width: 40,
              }}
            >
              Hrs :
            </SeaTypography>
          )}
        </SeaStack>

        {/* Minutes*/}
        <SeaStack
          direction={"row"}
          padding={0}
          gap={4}
          style={styles.fieldContainer}
        >
          <TextInput
            value={minutes}
            selectTextOnFocus={true}
            keyboardType={"number-pad"}
            onChangeText={(mins) => {
              setMinutes(mins);
              handleChange(hours, mins);
            }}
            placeholderTextColor={colors.text.placeholder}
            onFocus={onFocus}
            onBlur={onBlur}
            style={[
              styles.textInputStyle,
              platformOverrides,
              {
                width: 30,
              },
            ]}
          />
          {(isFocused || !!minutes || !!hours) && (
            <SeaTypography variant={"input"}>Mins</SeaTypography>
          )}
        </SeaStack>
      </View>

      <ErrorText hasError={hasError} text={errorText} />
    </View>
  );
};

const HEIGHT = 40;
const styleSheet = createStyleSheet((theme) => ({
  container: {
    width: "100%",
  },
  labelContainer: {
    marginBottom: 4,
    height: 12,
  },
  labelText: {
    textTransform: "uppercase",
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 11,
    fontWeight: "500",
    lineHeight: 12,
    letterSpacing: 0.75,
    color: colors.text.secondary,
  },
  inputContainer: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    gap: 10,
    height: HEIGHT,
    width: "100%",
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  inputContainerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  fieldContainer: {},
  hoursText: {},
  textInputStyle: {
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 14,
    lineHeight: 18,
    textAlign: "left",
    letterSpacing: 0.15,
    fontWeight: "400",
    width: "100%",
    color: colors.text.primary,
  },
}));
