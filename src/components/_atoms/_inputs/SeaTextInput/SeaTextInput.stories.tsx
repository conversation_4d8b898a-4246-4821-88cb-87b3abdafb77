import type { Meta, StoryObj } from "@storybook/react";
import React, { useEffect, useState } from "react";
import { View } from "react-native";
import { SeaTextInput } from "@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput";
import { FontAwesome } from "@expo/vector-icons";
import { colors } from "@src/theme/colors";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";

const meta = {
  title: "Components/Atoms/SeaTextInput",
  component: SeaTextInput,
  argTypes: {
    multiLine: {
      control: "boolean",
      description: "Whether to show as multiline input",
    },
  },
  args: {},
  decorators: [
    (Story) => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: "center",
            justifyContent: "center",
            alignContent: "center",
            width: "100%",
            height: "100%",
          }}
        >
          <View style={{ width: 300 }}>
            <Story />
          </View>
        </View>
      );
    },
  ],
} satisfies Meta<typeof SeaTextInput>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: (args) => {
    const [value, setValue] = useState("");
    useEffect(() => {
      setValue(args.value);
    }, [args.value]);

    return (
      <SeaTextInput
        value={value}
        label={args.label}
        onChangeText={setValue}
        prefixIcon={args.prefixIcon}
        multiLine={args.multiLine}
      />
    );
  },
  args: {
    value: "",
    onChangeText: () => {},
    multiLine: false,
  },
  parameters: {
    backgrounds: {
      default: "Grey",
    },
  },
};

export const WithLabel: Story = {
  ...Default,
  args: {
    ...Default.args,
    label: "Example Text Input",
  },
};
export const WithIcon: Story = {
  ...Default,
  args: {
    ...Default.args,
    prefixIcon: (
      <SeaIcon icon={"search"} size={20} color={colors.text.primary} />
    ),
  },
};
