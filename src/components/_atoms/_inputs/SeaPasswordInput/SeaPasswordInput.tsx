import React, { useState } from "react";
import { TouchableOpacity, View } from "react-native";
import {
  SeaTextInput,
  SeaInputProps,
} from "@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput";
import { SeaIcon } from "@src/components/_atoms/SeaIcon/SeaIcon";
import { createStyleSheet, useStyles } from "@src/theme/styles";

export interface SeaPasswordInputProps
  extends Omit<SeaInputProps, "secureTextEntry"> {
  showToggle?: boolean;
  toggleIconSize?: number;
}

export const SeaPasswordInput: React.FC<SeaPasswordInputProps> = ({
  showToggle = true,
  toggleIconSize = 20,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const { styles } = useStyles(styleSheet);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const passwordIcon = showToggle ? (
    <TouchableOpacity onPress={togglePasswordVisibility}>
      <SeaIcon
        icon={showPassword ? "visibility_off" : "visibility"}
        size={toggleIconSize}
      />
    </TouchableOpacity>
  ) : undefined;

  return (
    <SeaTextInput
      {...props}
      secureTextEntry={!showPassword}
      suffixIcon={passwordIcon}
      inputContainerStyle={showToggle ? styles.inputContainer : undefined}
    />
  );
};

const styleSheet = createStyleSheet((theme) => ({
  inputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
}));
