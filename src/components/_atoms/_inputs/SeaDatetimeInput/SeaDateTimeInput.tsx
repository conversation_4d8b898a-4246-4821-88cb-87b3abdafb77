import React from "react";
import { Platform, StyleSheet, View, ViewStyle } from "react-native";
import { DateTime } from "luxon";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { ErrorText } from "../ErrorText/ErrorText";
import RNDateTimePicker from "@react-native-community/datetimepicker";

interface SeaDateTimeProps {
  value: DateTime;
  onChange: (date: DateTime) => void;
  type: "date" | "datetime";
  style?: ViewStyle;
  label?: string;
  disabled?: boolean;
  hasError?: boolean;
  errorText?: string;
}

export const SeaDateTimeInput: React.FC<SeaDateTimeProps> = ({
  value,
  onChange,
  type,
  style,
  label,
  disabled = false,
  hasError = false,
  errorText,
}) => {
  if (Platform.OS === "web") {
    return (
      <WebDateTime
        disabled={disabled}
        label={label}
        value={value}
        onChange={onChange}
        type={type}
        style={style}
        hasError={hasError}
        errorText={errorText}
      />
    );
  }
  return (
    <NativeDateTime
      value={value}
      onChange={onChange}
      type={type}
      style={style}
      label={label}
      disabled={disabled}
      hasError={hasError}
      errorText={errorText}
    />
  );
};

const styles = StyleSheet.create({
  container: {},
});

const NativeDateTime = ({
  value,
  onChange,
  type,
  style,
  label = "Due Date",
  disabled,
  hasError = false,
  errorText,
}: SeaDateTimeProps) => {
  const { theme, styles } = useStyles(webStylesheet);
  return (
    <View style={style}>
      <SeaTypography variant={"label"}>{label.toUpperCase()}</SeaTypography>
      <View
        style={[
          styles.container,
          { opacity: disabled ? 0.5 : 1 },
          hasError && styles.containerError,
        ]}
      >
        <RNDateTimePicker
          value={value.toJSDate()}
          mode={type}
          onChange={(event) => {
            onChange(DateTime.fromMillis(event.nativeEvent.timestamp));
          }}
          themeVariant={"light"}
          textColor={theme.colors.text.primary}
          style={
            {
              // backgroundColor: "magenta",
            }
          }
        />
      </View>
      <ErrorText hasError={hasError} text={errorText} />
    </View>
  );
};

const WebDateTime = ({
  value,
  onChange,
  type = "date",
  style,
  label = "Due Date",
  disabled = false,
  hasError = false,
  errorText,
}: {
  value: DateTime;
  onChange: (value: DateTime) => void;
  type: "date" | "datetime";
  style?: ViewStyle;
  label?: string;
  disabled?: boolean;
  hasError?: boolean;
  errorText?: string;
}) => {
  const { styles } = useStyles(webStylesheet);
  return (
    <View style={style}>
      <SeaTypography variant={"label"}>{label.toUpperCase()}</SeaTypography>
      <View
        style={[
          styles.container,
          { opacity: disabled ? 0.5 : 1 },
          hasError && styles.containerError,
        ]}
      >
        <input
          disabled={disabled}
          type={type === "date" ? "date" : "datetime-local"}
          value={
            type === "date"
              ? value.toISODate()
              : value.toLocal().toFormat("yyyy-MM-dd'T'HH:mm")
          }
          onChange={(e) => {
            console.log("Value:", e.target.value);
            onChange(DateTime.fromISO(e.target.value));
          }}
          style={styles.input}
        />
      </View>
      <ErrorText hasError={hasError} text={errorText} />
    </View>
  );
};

const ROW_HEIGHT = 40;
const webStylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: "row",
    height: ROW_HEIGHT,
    width: "100%",
    backgroundColor: theme.colors.input.background,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    borderWidth: 1,
    gap: 10,
    paddingHorizontal: 12,
    justifyContent: "flex-start",
    alignItems: "center",
  },
  containerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  input: {
    // @ts-ignore outlineStyle is a web-only style prop
    outlineStyle: "none",
    borderWidth: 0,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 15,
    fontWeight: "400",
    color: theme.colors.text.primary,
  },
}));
