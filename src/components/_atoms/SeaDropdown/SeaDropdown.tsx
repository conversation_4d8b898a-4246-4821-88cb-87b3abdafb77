import {
  Mo<PERSON>,
  <PERSON><PERSON>View,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  useWindowDimensions,
  View,
  ViewStyle,
} from "react-native";
import React, {
  Fragment,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { useSharedValue, withTiming } from "react-native-reanimated";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaIcon } from "../SeaIcon/SeaIcon";
import { createStyleSheet, useStyles } from "@src/theme/styles";
import { SeaTypography } from "@src/components/_atoms/SeaTypography/SeaTypography";
import { SeaLineBreak } from "@src/components/_atoms/SeaLineBreak/SeaLineBreak";
import { ErrorText } from "@src/components/_atoms/_inputs/ErrorText/ErrorText";

const ROW_HEIGHT = 40;
const DROPDOWN_MAX_HEIGHT = 320;
const SAFETY_MARGIN = 20;

export interface SeaDropdownItem<T> {
  label: string;
  value: T;
}

export interface SeaDropdownProps<T> {
  items: SeaDropdownItem<T>[];
  onSelect: (value: T) => void;
  value?: T;
  initialValue?: T;
  label?: string;
  style?: StyleProp<ViewStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  prefixLabel?: string;
  rounded?: boolean;
  placeholder?: string;
  disabled?: boolean;
  hasError?: boolean;
  errorText?: string;
  noValidation?: boolean;
}

export const SeaDropdown = <T,>({
  items,
  onSelect,
  value,
  initialValue,
  label,
  style,
  containerStyle,
  textStyle,
  prefixLabel,
  rounded = false,
  placeholder = "Select an item",
  disabled = false,
  hasError = false,
  errorText,
  noValidation = false,
}: SeaDropdownProps<T>) => {
  const { styles, theme } = useStyles(styleSheet);
  const { width, height } = useWindowDimensions();
  const triggerRef = useRef<View | null>(null);
  const [refDimensions, setRefDimensions] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const [isOpen, setIsOpen] = useState(false);
  const [shouldRenderAbove, setShouldRenderAbove] = useState(false);

  const [selectedItem, setSelectedItem] = useState<
    SeaDropdownItem<T> | undefined
  >(items.find((x) => x.value === initialValue));

  const labelOpacity = useSharedValue(0);

  // Calculate available space and determine positioning
  const calculatePositioning = () => {
    if (!refDimensions.height) return;

    const spaceBelow = height - (refDimensions.y + refDimensions.height);
    const spaceAbove = refDimensions.y;
    const requiredSpace = DROPDOWN_MAX_HEIGHT + SAFETY_MARGIN;

    // If there's not enough space below, render above
    setShouldRenderAbove(
      spaceBelow < requiredSpace && spaceAbove >= requiredSpace,
    );
  };

  useLayoutEffect(() => {
    if (!triggerRef.current) return;
    triggerRef.current.measure((x, y, width, height, pageX, pageY) => {
      setRefDimensions({ x: pageX, y: pageY, width, height });
    });
  }, [width, height]);

  useLayoutEffect(() => {
    calculatePositioning();
  }, [refDimensions, height]);

  useEffect(() => {
    labelOpacity.value = withTiming(selectedItem ? 1 : 0, { duration: 200 });
  }, [selectedItem]);

  useEffect(() => {
    const selected = items.find((x) => x.value === value);
    setSelectedItem(selected ?? undefined);
  }, [value]);

  // Calculate dropdown positioning
  const getDropdownPosition = () => {
    if (shouldRenderAbove) {
      return {
        position: "absolute" as const,
        bottom: height - refDimensions.y,
        left: refDimensions.x,
        width: refDimensions.width,
      };
    } else {
      return {
        position: "absolute" as const,
        top: refDimensions.y + refDimensions.height,
        left: refDimensions.x,
        width: refDimensions.width,
      };
    }
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <SeaTypography variant={"label"}>{label.toUpperCase()}</SeaTypography>
      )}

      <TouchableOpacity
        onPress={() => {
          !disabled
            ? triggerRef?.current?.measure(
                (x, y, width, height, pageX, pageY) => {
                  setRefDimensions({ x: pageX, y: pageY, width, height });
                  setIsOpen(!isOpen);
                },
              )
            : null;
        }}
        ref={triggerRef}
        style={[
          styles.dropdownContainer,
          rounded && styles.roundedDropdown,
          disabled && { opacity: 0.5 },
          hasError && styles.dropdownContainerError,
          containerStyle,
        ]}
      >
        <SeaStack
          direction="row"
          justify="between"
          align="center"
          style={{ paddingHorizontal: 12 }}
          gap={8}
        >
          <Text style={[styles.triggerText, textStyle]}>
            {prefixLabel ? `${prefixLabel} ` : ""}
            <Text style={[styles.triggerTextLight, textStyle]}>
              {selectedItem?.label ?? placeholder}
            </Text>
          </Text>
          <SeaIcon icon="arrow_drop_down" />
        </SeaStack>
      </TouchableOpacity>

      {!noValidation ? (
        <ErrorText hasError={hasError} text={errorText} />
      ) : (
        <></>
      )}

      <Modal
        visible={isOpen}
        onRequestClose={() => setIsOpen(false)}
        transparent
      >
        <TouchableOpacity
          onPress={() => setIsOpen(false)}
          style={styles.modalOverlay}
          activeOpacity={1}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={[styles.dropdownList, getDropdownPosition()]}
          >
            {items.map((item, index) => {
              const isSelected = selectedItem?.value === item.value;
              return (
                <Fragment key={`${item.value}-${index}`}>
                  <TouchableOpacity
                    onPress={() => {
                      if (isSelected) {
                        // Deselect if the user taps again
                        setSelectedItem(undefined);
                        onSelect(undefined as T);
                        setIsOpen(false);
                        return;
                      }

                      setSelectedItem(item);
                      onSelect(item.value);
                      setIsOpen(false);
                    }}
                    style={[
                      styles.itemRow,
                      isSelected && { backgroundColor: theme.colors.primary },
                    ]}
                  >
                    <Text
                      style={[
                        styles.itemText,
                        isSelected && { color: theme.colors.text.isSelected },
                      ]}
                      lineBreakMode="tail"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.label}
                    </Text>
                  </TouchableOpacity>
                  <SeaLineBreak />
                </Fragment>
              );
            })}
          </ScrollView>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styleSheet = createStyleSheet((theme) => ({
  container: {},
  dropdownContainer: {
    height: ROW_HEIGHT,
    borderRadius: 8,
    borderWidth: 1,
    width: "100%",
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    justifyContent: "center",
  },
  dropdownContainerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  roundedDropdown: {
    borderRadius: 999,
  },
  triggerText: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    fontWeight: 400,
    color: theme.colors.text.primary,
  },
  triggerTextLight: {
    fontSize: 14,
    fontWeight: "400",
    color: "#616367",
  },
  modalOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.2)",
  },
  dropdownList: {
    backgroundColor: theme.colors.input.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 320,
  },
  itemRow: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  itemText: {
    fontSize: 14,
    color: theme.colors.text.primary,
  },
}));
