"use dom";

import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  $copyNode,
  $getRoot,
  createEditor,
  EditorState,
  LexicalEditor,
  LexicalNode,
} from "lexical";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import LexicalViewerEditorComposer from "@src/components/_dom/lexical/SeaRichText/LexicalViewerEditorComposer";
import { SeaRichTextProps } from "@src/components/_dom/lexical/SeaRichText/SeaRichText";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import LexDragDropPaste from "@src/components/_dom/lexical/plugins/LexDragDropPaste/LexDragDropPaste";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { View } from "react-native";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { useFonts } from "expo-font";
import { ToastType } from "@src/managers/ToastManager/ToastManager";

// 19cm = 7.48031 inches. for 300DPI, need 2244.093
// therefore, 760 pixels on screen needs 2244.093 pixels of resolution for print
// therefore 1 screen pixel needs 2.952753947368421 pixels. Just use 3x ratio?
// so, approximately 3X pixels per screen pixel required.
// [] Do not convert SVG

const scrollToSection = (tag: string, key: string, smooth = true) => {
  let target = document.getElementById(`${tag}_${key}`);
  if (
    tag === "h2" &&
    target?.previousSibling &&
    (target.previousSibling as any).offsetHeight &&
    (target.previousSibling as any).offsetHeight < 50
  ) {
    target = (target as any).previousSibling;
    target?.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
      block: "start",
    });
  } else {
    target?.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
      block: "start",
    });
  }
};

const extractHeadingText = (node: LexicalNode | null | undefined) => {
  if (node) {
    let s = "";
    if (
      node.__text &&
      !(node.__format & (1 << 5)) && // is not subscript format
      !(node.__format & (1 << 6)) // is not superscript format
    ) {
      s += node.__text;
    }
    if (node.getFirstChild && node.getFirstChild()) {
      s += extractHeadingText(node.getFirstChild());
    }
    if (node.getNextSibling()) {
      s += extractHeadingText(node.getNextSibling());
    }
    return s ? s : "...";
  }
  return "...";
};

interface SeaRichTextViewerProps extends SeaRichTextProps {
  setSections: (sections: any[]) => void;
  scrollHeaderSection: {
    tag: string;
    key: string;
    smooth: boolean;
  };
  isEditable?: boolean;
}

const SeaRichTextViewer = ({
  // forModal,
  // visible,
  // setOnScroll,
  richTextState,
  // modalContentRef,
  // editButtons,
  // onlineStatus = false,
  setSections,
  scrollHeaderSection,
  isEditable = false,
  onSaveChanges,
  showToast,
}: SeaRichTextViewerProps) => {
  const isMounted = useRef(false);
  const [activeSection, setActiveSection] = useState<string>();
  const sectionsRef = useRef<any[]>();
  const activeSectionRef = useRef<string>();
  const [loadedVersion, setLoadedVersion] = useState<number | undefined>();

  const editorRef = useRef<LexicalEditor>();
  const editorStateRef = useRef<EditorState>();
  const onChangeIdRef = useRef(0); // Give an onChange process an id so it can be skipped if a newer job starts

  const containerRef = useRef<HTMLDivElement>(null);
  const onScrollJobRef = useRef(0);
  const scrollOnChange = useRef(false); // If currently true, a change in activeSection will scroll to that section

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (scrollHeaderSection) {
      scrollToSection(
        scrollHeaderSection.tag,
        scrollHeaderSection.key,
        scrollHeaderSection.smooth,
      );
    }
  }, [scrollHeaderSection]);

  function EditorRefPlugin() {
    // Simply for getting a reference to our LexicalEditor
    const [editor] = useLexicalComposerContext();
    editorRef.current = editor;
    return null;
  }

  // useEffect(() => {
  //   activeSectionRef.current = activeSection;
  // }, [activeSection]);
  //
  // const onScroll = useCallback(() => {
  //   const y = containerRef.current?.getBoundingClientRect().top;
  //
  //   let headerHeight;
  //   if (forModal) {
  //     headerHeight = 80;
  //   } else {
  //     headerHeight = 44 + getSafeInsetTop();
  //   }
  //   if (y !== undefined && y < headerHeight) {
  //     // if element scrolled above header bar (44px)
  //     // Fixed mode
  //     containerRef.current?.classList.add("fixed");
  //   } else {
  //     // Normal mode
  //     containerRef.current?.classList.remove("fixed");
  //   }
  //
  //   const jobId = ++onScrollJobRef.current;
  //   setTimeout(() => {
  //     if (!isMounted.current) return;
  //     if (
  //       sectionsRef.current &&
  //       sectionsRef.current.length > 0 &&
  //       jobId === onScrollJobRef.current
  //     ) {
  //       // Scrolling finished. Let's see if the current section needs updating.
  //       let index = 0;
  //       while (index < sectionsRef.current.length - 1) {
  //         const target = document.getElementById(
  //           `${sectionsRef.current[index + 1].tag}_${sectionsRef.current[index + 1].key}`,
  //         );
  //
  //         if (target && target.getBoundingClientRect().top > 150) {
  //           break;
  //         }
  //         index++;
  //       }
  //       if (sectionsRef.current[index].key !== activeSectionRef.current) {
  //         scrollOnChange.current = false;
  //         setActiveSection(sectionsRef.current[index].key);
  //       }
  //     }
  //   }, 50);
  // }, [forModal]);

  // useEffect(() => {
  //   if (visible && forModal) {
  //     scrollOnChange.current = false;
  //     // Make sure 'fixed' class is added after DOM elements have been drawn
  //     setTimeout(() => {
  //       if (!isMounted.current) return;
  //       onScroll();
  //     }, 10);
  //     setTimeout(() => {
  //       if (!isMounted.current) return;
  //       onScroll();
  //     }, 100);
  //     setTimeout(() => {
  //       if (!isMounted.current) return;
  //       onScroll();
  //     }, 250);
  //     setTimeout(() => {
  //       if (!isMounted.current) return;
  //       onScroll();
  //     }, 500);
  //     setTimeout(() => {
  //       if (!isMounted.current) return;
  //       onScroll();
  //     }, 1000);
  //   }
  // }, [visible, forModal, onScroll]);

  // const scrollToTop = useCallback(() => {
  //   scrollOnChange.current = false;
  //   if (sections && sections.length > 0) {
  //     setActiveSection(sections[0].key);
  //   }
  //   if (forModal) {
  //     if (modalContentRef?.current) {
  //       modalContentRef.current.scrollTo({
  //         y: 0,
  //       });
  //     }
  //   } else {
  //     // scrollPageToTop(300);
  //   }
  // }, [sections, forModal, modalContentRef]);

  // useEffect(() => {
  //   if (visible) {
  //     setOnScroll(() => {
  //       // We must pass onScroll via a function because setOnScroll will call the function to get the value
  //       return onScroll;
  //     });
  //     onScroll();
  //   } else {
  //     setOnScroll(undefined);
  //   }
  // }, [visible, setOnScroll, onScroll]);

  useEffect(() => {
    if (
      richTextState?.loadedJson &&
      richTextState.loadedVersion !== loadedVersion
    ) {
      if (editorRef.current) {
        // If the User is on the Edit Screen, show a toast message
        if (isEditable && loadedVersion !== undefined) {
          showToast({
            message: "Document has been updated by another user.",
            type: ToastType.ERROR,
          });
        }
        // Set the current loaded version to stop the continuous re-render and also trigger an update to the document
        setLoadedVersion(richTextState.loadedVersion);

        try {
          // Load the rich text doc into the editor
          const editorState = editorRef.current.parseEditorState(
            richTextState.loadedJson,
          );

          // Set the refs for the editor and the editor state
          editorRef.current?.setEditorState(editorState);
          editorStateRef.current = editorRef.current.getEditorState();

          // Process the lexical tree from the loaded editor state
          processLexicalTree();

          // TODO: check if necessary
          // Set Initial Sections
        } catch (error) {
          console.error("Error parsing editor state:", error);
          // Create a fallback empty editor state
          const fallbackState = createEditor().parseEditorState(
            JSON.stringify({
              root: {
                children: [
                  {
                    children: [],
                    direction: null,
                    format: "",
                    indent: 0,
                    type: "paragraph",
                    version: 1,
                  },
                ],
                direction: null,
                format: "",
                indent: 0,
                type: "root",
                version: 1,
              },
            }),
          );
          editorRef.current.setEditorState(fallbackState);
          editorStateRef.current = editorRef.current.getEditorState();

          // TODO: check if necessary
          // Set Initial Sections
          setSections([]);
          setActiveSection(undefined);
        }
      } else {
        console.error("editorRef.current missing!");
      }
    }
  }, [
    // Intentionally not passing the richTextState here
    // On Native, this keeps looping and hits a memory leak
    richTextState?.loadedJson,
  ]);

  /**
   * This function loads the nodes from the Lexical Tree
   * Also sets gets the sections to display in the Table of contents
   */
  const processLexicalTree = useCallback(() => {
    if (editorStateRef?.current) {
      editorStateRef.current.read(() => {
        const root = $getRoot();
        const _sections = [] as any[];

        let needsFixing = false;
        const scanNode = (node: LexicalNode | null | undefined) => {
          while (node) {
            /** Check for nodes that need to be removed */
            if (isEditable) {
              if (node.__type === "link") {
                needsFixing = true; // Don't want links (at least for now)
              } else if (
                node.__type === "image" &&
                !node.__src.startsWith("data")
              ) {
                needsFixing = true; // Don't want images that don't contain data
              }
            }

            // Get sections titles
            if (node.__tag === "h1" || node.__tag === "h2") {
              if (node?.getFirstChild) {
                _sections.push({
                  title: extractHeadingText(node.getFirstChild()),
                  key: node.__key,
                  tag: node.__tag,
                });
              }
            }

            if (node.getFirstChild?.()) {
              scanNode(node.getFirstChild());
            }

            // Move to next sibling
            node = node.getNextSibling();
          }
        };

        //debugNodeTree(root.getFirstChild());
        scanNode(root.getFirstChild());
        setSections(_sections);
        sectionsRef.current = _sections;
        //scrollOnChange.current = false;

        // TODO: Check if necessary
        /** Set the Initial Sections */
        // Check if this is still needed
        // if (_sections.length > 0 && activeSection === undefined) {
        //   setActiveSection(_sections[0].key);
        // } else {
        //   setActiveSection(undefined);
        // }

        if (isEditable) {
          if (needsFixing && editorRef.current) {
            editorRef.current.update(() => {
              const fixNode = (node: LexicalNode | null | undefined) => {
                while (node) {
                  const nextSibling = node.getNextSibling();
                  if (node.__type === "link") {
                    console.log("Auto removing link node", node);
                    const clones = [] as LexicalNode[];
                    if (node.getChildren?.()) {
                      node.getChildren().forEach((child: LexicalNode) => {
                        clones.push($copyNode(child));
                      });
                    }
                    clones.forEach((clone: LexicalNode) => {
                      // @ts-ignore
                      node.insertBefore(clone);
                    });
                    node.remove();
                  } else if (
                    node.__type === "image" &&
                    !node.__src.startsWith("data")
                  ) {
                    console.log("Auto removing image with no data", node);
                    node.remove();
                  } else if (node.getFirstChild?.()) {
                    fixNode(node.getFirstChild());
                  }
                  node = nextSibling;
                }
              };
              fixNode(root.getFirstChild());
            });
          }
        }
      });
    }
  }, [
    // activeSection,
    editorStateRef,
  ]);

  // const changeSection = (increment: number) => {
  //   if (activeSection && sections && sections.length > 0) {
  //     let index = 0;
  //     for (let i = 0; i < sections.length; i++) {
  //       if (activeSection === sections[i].key) {
  //         index = i;
  //         break;
  //       }
  //     }
  //     index = index + increment;
  //     if (index === -1) {
  //       scrollToTop();
  //       return;
  //     }
  //     index = Math.min(index, sections.length - 1);
  //     index = Math.max(index, 0);
  //     scrollOnChange.current = false;
  //     setActiveSection(sections[index].key);
  //     scrollToSection(sections[index].tag, sections[index].key, true);
  //   }
  // };

  /** Edit Plugins */
  const onLexicalChange = useCallback(
    (editorState: EditorState, editor: LexicalEditor) => {
      editorRef.current = editor;
      editorStateRef.current = editorState;

      const onChangeId = ++onChangeIdRef.current;
      setTimeout(() => {
        if (onChangeId === onChangeIdRef.current) {
          processLexicalTree();
        }
      }, 500);
    },
    [processLexicalTree],
  );

  /** Load the fonts to be used in the WebView */
  const [fontsLoaded] = useFonts({
    body: require("@assets/fonts/Inter-VariableFont.ttf"),
    title: require("@assets/fonts/Poppins-Regular.ttf"),
    titleBold: require("@assets/fonts/Poppins-Bold.ttf"),
    titleLight: require("@assets/fonts/Poppins-Light.ttf"),
    titleMedium: require("@assets/fonts/Poppins-Medium.ttf"),
    titleSemiBold: require("@assets/fonts/Poppins-SemiBold.ttf"),
    materialIconOutlined: require("@assets/fonts/google-material-icons/MaterialSymbolsOutlined.ttf"),
    materialIconRounded: require("@assets/fonts/google-material-icons/MaterialSymbolsRounded.ttf"),
    materialIconSharp: require("@assets/fonts/google-material-icons/MaterialSymbolsSharp.ttf"),
  });

  return (
    <View
      style={{
        flexDirection: "column",
        flex: 1,
        height: "100%",
        gap: 10,
      }}
    >
      {isEditable && (
        <SeaStack direction={"column"} align={"end"} isCollapsible={true}>
          <SeaButton
            label={"Save"}
            onPress={() => {
              if (editorStateRef.current) {
                const json = editorStateRef.current?.toJSON();
                onSaveChanges && onSaveChanges(json);
              }
            }}
            iconOptions={{
              icon: "save",
            }}
          />
        </SeaStack>
      )}

      <LexicalViewerEditorComposer isEditable={isEditable}>
        <EditorRefPlugin />
        {isEditable && (
          <>
            <LexDragDropPaste />
            <AutoFocusPlugin />
            <OnChangePlugin
              onChange={onLexicalChange}
              ignoreSelectionChange={true}
            />
            {/*{floatingAnchorElem && (*/}
            {/*    <>*/}
            {/*      <LexDraggableBlock anchorElem={floatingAnchorElem} />*/}
            {/*      <LexTableActionMenu anchorElem={floatingAnchorElem} />*/}
            {/*    </>*/}
            {/*)}*/}
            <HistoryPlugin
            // TODO: most likely not necessary
            // externalHistoryState={historyState}
            />
          </>
        )}
      </LexicalViewerEditorComposer>
    </View>
  );
};

export default SeaRichTextViewer;
