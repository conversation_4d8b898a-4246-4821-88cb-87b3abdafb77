import React, { useState } from "react";
import { View, StyleSheet } from "react-native";
import { SeaModal } from "@src/components/_atoms/SeaModal/SeaModal";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { LexicalEditor } from "lexical";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaLabelValue } from "@src/components/_atoms/SeaLabelValue/SeaLabelValue";
import SeaLabel from "@src/components/_legacy/SeaLabel/SeaLabel";

export interface LexColourPickerModalProps {
  visible: boolean;
  onClose: () => void;
  editor: LexicalEditor;
}

const lexColorSwatches = [
  "#000000",
  "#2b2b2b",
  "#555555",
  "#808080",
  "#aaaaaa",
  "#d5d5d5",
  "#ffffff",
  "#92d051",
  "#fdf054",
  "#fbbe43",
  "#fb4322",
  "#9bc2e6",
  "#be80fe",
  "#fe80e3",
];

export const LexColourPickerModal = ({
  visible,
  onClose,
  editor,
}: LexColourPickerModalProps) => {
  const [color, setColor] = useState("#ffffff");

  return (
    <SeaModal
      visible={visible}
      onClose={onClose}
      title={"Highlight Colour"}
      style={{ maxWidth: "60%" }}
    >
      <SeaStack
        direction={"column"}
        align={"start"}
        justify={"center"}
        gap={10}
      >
        <SeaStack direction={"column"} align={"start"} width={"100%"}>
          <SeaLabel>Colour Chosen</SeaLabel>
          <View
            key={color}
            style={[
              styles.colourContainer,
              {
                width: "100%",
                backgroundColor: color,
              },
            ]}
          />
        </SeaStack>

        <SeaStack direction={"column"} align={"start"} width={"100%"}>
          <SeaLabel>Swatches</SeaLabel>
          <SeaStack
            direction={"row"}
            gap={2}
            width={"100%"}
            align={"start"}
            // justify={"center"}
            style={{
              flexWrap: "wrap",
            }}
          >
            {lexColorSwatches.map((lexColor) => (
              <View
                // key={color}
                key={lexColor}
                style={[styles.colourContainer, { backgroundColor: lexColor }]}
              />
            ))}
          </SeaStack>
        </SeaStack>
        <SeaStack direction={"column"} align={"start"} width={"100%"}>
          <SeaLabel>Customise</SeaLabel>
        </SeaStack>

        <SeaButton
          label={"Choose Colour"}
          onPress={() => onClose()}
          // textStyle={{ flex: 1 }}
        />
      </SeaStack>
    </SeaModal>
  );
};

const styles = StyleSheet.create({
  colourContainer: {
    height: 40,
    width: 40,
    backgroundColor: "white",
    borderColor: "#E2E2E2",
    borderRadius: 8,
    borderWidth: 1,
    // display: "flex",
    alignItems: "center",
    justifyContent: "center",
    // flex: 1,
    // flexWrap: "wrap",
  },
});
