import React, { use<PERSON><PERSON>back, useMemo } from "react";
import { SeaModal } from "@src/components/_atoms/SeaModal/SeaModal";
import { SeaTextInput } from "@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput";
import { SeaStack } from "@src/components/_atoms/SeaStack/SeaStack";
import { SeaButton } from "@src/components/_atoms/SeaButton/SeaButton";
import { INSERT_TABLE_COMMAND } from "@lexical/table";
import { useFormik } from "formik";
import { LexicalEditor } from "lexical";
import Yup from "@src/lib/yup";

const validationSchema = Yup.object({
  rows: Yup.number().min(1).max(10).required(),
  columns: Yup.number().min(1).max(10).required(),
});

export interface LexNewTableModalProps {
  visible: boolean;
  onClose: () => void;
  editor: LexicalEditor;
}

export const LexNewTableModal = ({
  visible,
  onClose,
  editor,
}: LexNewTableModalProps) => {
  const initialValues = useMemo(() => {
    return {
      rows: 5,
      columns: 5,
    };
  }, []);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values) => handleSubmit(values),
  });

  const handleSubmit = useCallback(
    (values: { rows: number; columns: number }) => {
      editor?.dispatchCommand(INSERT_TABLE_COMMAND, {
        rows: values.rows.toString(),
        columns: values.columns.toString(),
      });
      onClose();
    },
    [editor, onClose],
  );

  return (
    <SeaModal
      title="Insert New Table"
      visible={visible}
      onClose={onClose}
      style={{ maxWidth: "60%" }}
    >
      <SeaStack
        direction={"column"}
        align={"center"}
        justify={"center"}
        gap={10}
      >
        <SeaTextInput
          label={"Number of Rows"}
          value={formik.values.rows.toString()}
          onChangeText={(value) => formik.setFieldValue("rows", value)}
          keyboardType="number-pad"
          hasError={Boolean(formik.errors.rows)}
          errorText={formik.errors.rows}
        />
        <SeaTextInput
          label={"Number of Columns"}
          value={formik.values.columns.toString()}
          onChangeText={(value) => formik.setFieldValue("columns", value)}
          keyboardType="number-pad"
          hasError={Boolean(formik.errors.columns)}
          errorText={formik.errors.columns}
        />
        <SeaButton
          label={"Insert Table"}
          onPress={() => formik.handleSubmit()}
        />
      </SeaStack>
    </SeaModal>
  );
};
