import React, { useMemo } from "react";
import {
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from "@src/components/_atoms/SeaTable/SeaTable";
import { Voyage } from "@src/shared-state/VesselLogbook/voyages";
import { formatDateShort } from "@src/lib/datesAndTime";
import { SeaTableIcon } from "@src/components/_atoms/SeaTable/SeaTableIcon";
import { renderFullNameForUserId } from "@src/shared-state/Core/users";

export interface VoyageHistoryTableProps {
  voyages: Voyage[];
  onVoyageSelect?: (voyage: Voyage) => void;
}

export const VoyageHistoryTable: React.FC<VoyageHistoryTableProps> = ({
  voyages,
  onVoyageSelect,
}) => {
  const columns = useMemo<SeaTableColumn<Voyage>[]>(
    () => [
      {
        label: "Voyage Name",
        value: (voyage) => {
          const masters = voyage.masterIds
            .map((id) => renderFullNameForUserId(id))
            .join(", ");
          return `${voyage.name} - ${masters}`;
        },
        icon: () => <SeaTableIcon icon={"directions_boat_filled"} />,
      },
      {
        label: "Departure",
        value: (voyage) =>
          `${formatDateShort(voyage.whenDeparted)} - ${voyage.departureFrom}`,
        icon: () => <SeaTableIcon icon={"place"} />,
      },
      {
        label: "Arrival",
        value: (voyage) =>
          `${formatDateShort(voyage.whenArrived)} - ${voyage.destinationTo ?? ""}`,
        icon: () => <SeaTableIcon icon={"place"} />,
      },
    ],
    [],
  );

  const rows = useMemo<SeaTableRow<Voyage>[]>(() => {
    return voyages.map((voyage) => ({
      data: voyage,
      onPress: onVoyageSelect ? () => onVoyageSelect(voyage) : undefined,
    }));
  }, [voyages, onVoyageSelect]);

  return <SeaTable columns={columns} rows={rows} />;
};
