import {
  SharedStateConfig,
  runSharedState,
  sharedState,
} from "@src/shared-state/shared-state";
import {
  LicenseeDataSyncCollection,
  VesselDataSyncCollection,
} from "./dataSyncTasks";
import { isWebPretendingToBeNative } from "../../App";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";

//
// Maintains cachedDataInfo which stores when the last time various types of data have been loaded onto the device.
// This is how we know we'll need to get fresh data so that we're ready to go offline.
// This system is only relevant for ios/android apps AND if licenseeSettings.hasOffline is true.
//
// cachedDataInfo is kept in sync with localStorage via items called _<userId>_licensee and _<userId>_vessel_<vesselId>
//
// Note: A Firestore Timestamp represents a point in time independent of any time zone or calendar,
// represented as seconds and fractions of seconds at nanosecond resolution in UTC Epoch time.
// Use Timestamp.fromDate or fromMillis to make one. Use toMillis() to convert to epoch millis.
//

type CachedDataInfo = {
  licensee: Partial<Record<LicenseeDataSyncCollection, number>>;
  vessels: Record<string, Partial<Record<VesselDataSyncCollection, number>>>;
};

export const cachedDataInfo = {
  licensee: {},
  vessels: {},
} as CachedDataInfo;

/**
 * TODO: Make this !isProduction for proper firebase implementation
 */
export const profileDataSync = true; // Todo! Should be false for production builds
export type DataSyncOperation = 'getAll' | 'getLatest';
export type DataSyncPerformance = [
    when: number,
    operation: DataSyncOperation, // getAll or getLatest
    docs: number, // number of docs retrieved by all queries
    loadTime: number, // time to load data (round trip)
    processTime: number, // time to process loaded data
    errors?: string[], // error messages
];
export const dataSyncProfiling = {
  licensee: {},
  vessels: {},
} as {
  licensee: Partial<Record<LicenseeDataSyncCollection, DataSyncPerformance[]>>;
  vessels: Record<
    string,
    Partial<Record<VesselDataSyncCollection, DataSyncPerformance[]>>
  >;
}; // Data to store profiling info into

export const updateLicenseeCachedDataInfo = (
  collection: LicenseeDataSyncCollection,
  touched: number | undefined,
) => {
  cachedDataInfo.licensee[collection] = touched;
  return AsyncStorage.setItem(
    `_${sharedState.userId.current}_licensee`,
    JSON.stringify(cachedDataInfo.licensee),
  );
};

export const updateVesselCachedDataInfo = (
  vesselId: string,
  collection: VesselDataSyncCollection,
  touched: number | undefined,
) => {
  cachedDataInfo.vessels[vesselId] ??= {};
  cachedDataInfo.vessels[vesselId][collection] = touched;
  return AsyncStorage.setItem(
    `_${sharedState.userId.current}_vessel_${vesselId}`,
    JSON.stringify(cachedDataInfo.vessels[vesselId]),
  );
};

export const handleCachedDataInfoConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  dependencies: ["userId", "vesselIds", "licenseeSettings"],
  default: "Not initialised",
  notes:
    "Initialises cachedDataInfo which stores how fresh the device's local cached data is",
  run: (done, set, clear) => {
    done();
    const userId = sharedState.userId.current;
    const vesselIds = sharedState.vesselIds.current;
    const licenseeSettings = sharedState.licenseeSettings.current;
    if (
      (Platform.OS === "web" && !isWebPretendingToBeNative) ||
      !licenseeSettings?.hasOffline
    ) {
      set(`Not applicable unless a hybrid app with offline mode enabled`);
      sharedState.isDataSyncActive.set(false);
    } else if (userId && vesselIds) {
      // Load cachedData
      AsyncStorage.getItem(`_${userId}_licensee`)
        .then((licenseeJson) => {
          cachedDataInfo.licensee = licenseeJson
            ? JSON.parse(licenseeJson)
            : {};
          const promises = [] as Promise<any>[];
          vesselIds.forEach((vesselId: string) => {
            promises.push(
              AsyncStorage.getItem(`_${userId}_vessel_${vesselId}`).then(
                (vesselJson) => {
                  cachedDataInfo.vessels[vesselId] = vesselJson
                    ? JSON.parse(vesselJson)
                    : {};
                },
              ),
            );
          });
          return Promise.all(promises);
        })
        .then(() => {
          set(
            `cachedDataInfo loaded for user ${userId} along with ${vesselIds.length} vessels`,
          );
        })
        .catch((e) => {
          set("Failed to load cachedDataInfo!");
        })
        .finally(() => {
          sharedState.isDataSyncActive.set(true);
        });
    } else {
      set(`Not ready.`);
    }
  },
};

export const handleObsoleteCachedDataConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  dependencies: ["user", "licenseeSettings"],
  default: "Not run.",
  notes:
    "Responsible for resetting the local data cache if permissions have changed.",
  run: (done, set, clear) => {
    done();
    const user = sharedState.user.current;
    const licenseeSettings = sharedState.licenseeSettings.current;
    if (!user?.whenRoleChanged || !user?.vesselIds) {
      set(`Not ready.`);
    } else if (!licenseeSettings?.hasOffline) {
      set(`Does not have offline mode.`);
    } else {
      AsyncStorage.getItem(`_${user.id}_whenRoleChanged`).then(
        (cachedWhenRoleChanged) => {
          if (
            !cachedWhenRoleChanged ||
            Number(cachedWhenRoleChanged) < user?.whenRoleChanged!
          ) {
            resetCachedDataInfo().then(() => {
              console.log(
                `cachedDataInfo has been reset due to a newer user.whenRoleChange=${user.whenRoleChanged} cachedWhenRoleChanged=${cachedWhenRoleChanged}`,
              );
              set(`Local data cache reset!`);
            });
          } else {
            set(
              `Local data cache is up to date with the latest userPermissions.`,
            );
          }
        },
      );
    }
  },
};

export const resetCachedDataInfo = () => {
  const user = sharedState.user.current;
  if (!user) throw Error("Missing user!");
  // Any existing cached data is now possibly obsolete due to new permissions.
  cachedDataInfo.licensee = {};
  cachedDataInfo.vessels = {};

  const promises = [] as Promise<any>[];
  promises.push(AsyncStorage.removeItem(`_${user.id}_licensee`));
  user.vesselIds?.forEach((vesselId: string) => {
    promises.push(AsyncStorage.removeItem(`_${user.id}_vessel_${vesselId}`));
  });

  return Promise.all(promises)
    .then(() => {
      return AsyncStorage.setItem(
        `_${user.id}_whenRoleChanged`,
        "" + user.whenRoleChanged,
      );
    })
    .then(() => {
      // Need to manually rerun whenLicenseeTouched & whenVesselTouched
      runSharedState("whenLicenseeTouched");
      runSharedState("whenVesselTouched");
    })
    .catch((e) => {
      console.log(`Failed to resetCachedDataInfo e`, e);
    });
};
