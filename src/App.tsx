import { Assets as NavigationAssets } from "@react-navigation/elements";
import { Asset } from "expo-asset";
import * as FileSystem from "expo-file-system";
import React, { useEffect } from "react";
import { Button, Platform, Text, TextInput, View } from "react-native";
import * as Updates from "expo-updates";
import {
  checkForIosRefreshProblem,
  setAppIsOkToRestart,
} from "./shared-state/General/diagnoseRefreshProblems";
import { getDayOffsetMillis } from "./lib/datesAndTime";
import { sharedState } from "./shared-state/shared-state";
import {
  getDeviceId,
  initPhysicalDeviceInfo,
} from "./shared-state/Core/deviceInfo";
import { initAppState } from "./shared-state/Core/appState";
import { initOnlineStatus } from "./shared-state/Core/onlineStatus";
import { initAuthentication } from "./shared-state/Core/authentication";
import {
  activateDebugger,
  initDebugView,
} from "./shared-state/General/debugView";
import ConfirmDialogManager, {
  confirmAction,
} from "./managers/ConfirmDialogManager/ConfirmDialogManager";
import { logoutUser } from "./shared-state/Core/user";
import { fontFamily } from "./theme/typography";
import { ServiceContainer } from "@src/domain/di/ServiceContainer";
import { IFirebase } from "@src/domain/IFirebase";
import { SERVICES } from "./domain/di/ServiceRegistry";
import * as Sentry from "@sentry/react-native";
import { initializeSentryMonitoring } from "@src/lib/Sentry";
import { useLogger } from "@src/providers/ServiceProvider";
import DebugViewManager from "./managers/DebugViewManager/DebugViewManager";
import { router } from "expo-router";
import { isNative } from "@src/lib/device";
import ToastManager from "@src/managers/ToastManager/ToastManager";
import { SavingStateManager } from "@src/managers/SavingStateManager/SavingStateManager";

initializeSentryMonitoring();

Asset.loadAsync([
  ...NavigationAssets,
  require("./assets/newspaper.png"),
  require("./assets/bell.png"),
]);

// This is the entry point for the app. It is responsible for initializing the app and rendering the root component.
// It also handles the error boundary and the navigation container.

export const isWebPretendingToBeNative = false; // If true, desktop will adopt some behaviours of the native app, such as full data and file syncing

// Override default Text styles globally
(Text as any).defaultProps = (Text as any).defaultProps ?? {};
(Text as any).defaultProps.style = {
  fontFamily: fontFamily.BODY_FONT,
  fontSize: 12,
};

// Optionally do the same for TextInput if needed
(TextInput as any).defaultProps = (TextInput as any).defaultProps ?? {};
(TextInput as any).defaultProps.style = {
  fontFamily: fontFamily.BODY_FONT,
  fontSize: 12,
};

async function getAvailableSpace() {
  if (Platform.OS === "web") {
    try {
      if (navigator?.storage?.estimate) {
        const estimate = await navigator.storage.estimate();

        // Convert to GB with 2 decimal places
        const formatToGB = (bytes: number | undefined) =>
          bytes ? Number((bytes / (1024 * 1024 * 1024)).toFixed(2)) : undefined;

        const storageEstimate = {
          browserQuota: formatToGB(estimate.quota), // Browser-allocated space
          browserUsage: formatToGB(estimate.usage), // Used browser storage
          browserAvailable:
            estimate.quota && estimate.usage
              ? formatToGB(estimate.quota - estimate.usage)
              : undefined,
        };

        console.log("Browser storage estimate (GB):", storageEstimate);

        return {
          size: estimate.quota,
          available: estimate.quota
            ? estimate.quota - (estimate.usage ?? 0)
            : undefined,
          exists: true,
          isDirectory: true,
          isBrowserStorage: true, // Flag to indicate this is browser storage
        };
      } else {
        console.log("Browser storage estimation not available");
        return null;
      }
    } catch (error) {
      console.error("Error getting browser storage estimate:", error);
      return null;
    }
  } else {
    try {
      const stats = await FileSystem.getFreeDiskStorageAsync();
      console.log("Mobile storage stats in GB:", stats / 1024 / 1024 / 1024);
    } catch (error) {
      console.error("Error getting available space:", error);
      return null;
    }
  }
}

export const refreshApp = async (reason: string) => {
  console.log(`Refreshing app: ${reason}`);

  // Notify any state management that app refresh is intentional
  setAppIsOkToRestart(true, `Refreshing app: ${reason}`);

  /** Clear the selected Vessel ID and details stored in shared state */
  sharedState.vesselId.set(undefined);
  sharedState.vessel.clear();

  if (Platform.OS === "web") {
    // For web platform, reload the page to ensure complete state reset
    // This prevents users from using browser back button to access authenticated pages
    window.history.replaceState(null, "", "/");
    window.location.reload();
  } else {
    try {
      if (!__DEV__ && Updates.reloadAsync) {
        // In production, use Expo Updates to reload the app
        await Updates.reloadAsync();
      }
    } catch (error) {
      console.error("Failed to reload the app:", error);
    }
  }
};

export const attemptLogout = () => {
  confirmAction(`Are you sure you want to log out?`, `Yes, logout`)
    .then(() => {
      sharedState.showBlankScreen.set(true);
      logoutUser()
        .then(async () => {
          console.log("Logged out!");

          await refreshApp("Logout reload.");

          if (isNative) {
            /** For Native Mobile App without a back button like Web, navigate to the Login Page
             * This is not necessary for Web as the browser history is cleared and the page is reloaded
             * in the above function `refreshApp`
             */
            router.navigate("/login");
          }
        })
        .catch(async (error: any) => {
          console.log("failed to log out", error);
          await refreshApp("Failed to logout");
        });
    })
    .catch((e) => {
      console.error("Logout cancelled:", e);
    });
};

export default Sentry.wrap(function App({
  children,
}: {
  children: React.ReactNode;
}) {
  const logger = useLogger("App.tsx");
  const debugView = sharedState.debugView.use()!;

  // App initialisation
  useEffect(() => {
    getAvailableSpace();
    // checkForIosRefreshProblem(); // Now is the time to check diagnoseIosRefresh
    // setAppIsOkToRestart(false, "App init"); // Let diagnoseIosRefresh know that if a restart occurs it isn't expected

    // Keep todayMillis fresh. This is used to trigger state change when the current day has changed. Set to be 00:00 for the current day.
    const todayMillisInterval = setInterval(
      () => {
        sharedState.todayMillis.set(getDayOffsetMillis(0));
      },
      1 * 60 * 1000,
    );

    // Init deviceId
    getDeviceId().then((id: string) => {
      if (id) {
        sharedState.deviceId.set(id);
      }
    });

    // // Init physicalDeviceInfo.
    initPhysicalDeviceInfo();

    // // Init appState. Listens for when the app goes between the foreground (active) and background (inactive).
    const cleanupAppState = initAppState();

    // // Init online status. Listens for when the app switches between being online and offline.
    const cleanupOnlineStatus = initOnlineStatus();

    // // Init Authentication. Listens for Firebase authentication events.
    const cleanupAuthentication = initAuthentication();

    // // // Init debugger. User can see DebugView if they type debug333, or click 7 times in a row from left to right at the top of the screen
    const cleanupDebugView = initDebugView();

    // Cleanup
    return () => {
      setAppIsOkToRestart(true, "App cleanup"); // Let diagnoseIosRefresh that the next startup is due to a normal restart
      clearInterval(todayMillisInterval);
      try {
        cleanupAppState();
      } catch (e) {
        console.error("Error cleaning up app state:", e);
      }
      try {
        cleanupOnlineStatus();
      } catch (e) {
        console.error("Error cleaning up online status:", e);
      }
      try {
        cleanupAuthentication();
      } catch (e) {
        console.error("Error cleaning up authentication:", e);
      }
      try {
        cleanupDebugView();
      } catch (e) {
        console.error("Error cleaning up debugView:", e);
      }
    };
  }, []);

  return (
    <>
      {children}
      <ConfirmDialogManager />
      <ToastManager />
      <SavingStateManager />
      {debugView.isActive && <DebugViewManager />}
    </>
  );
}, {});
