import { isNative } from "@src/lib/device";

export type FileState = 0 | 1 | 2; // 0=not uploaded yet, 1=uploaded (original file), 2=converted into optimised versions
export type FileId = string; // (20 characters)
export type FileExt = string;
export type FileReference = string; // <FileState><FileId>_<OriginalFileName>.<FileExt>
// when stored in firestore within an images or documents field
// the value parsed as follows:
// <state><id>.<extension>
export interface SeaFile {
  // values that can de derived from firestore documents
  id?: FileId; // firestore id
  state?: FileState | number; // 0=pending upload, 1=uploaded, 2=uploaded and processed (has thumb version)
  ext?: FileExt; // extension like jpeg, png, pdf etc.
  // values also stored in firestore files collection
  // collection?: FileCollection; // firestore collection this is/will be attached to
  collection?: string; // firestore collection this is/will be attached to
  field?: string; // firestore collection.field this is/will be attached to
  docId?: string; // firestore doc id for which this is attached to. if undefined, it means is not attached
  whenAdded?: Number; // when file is added (before it is uploaded)
  whenUploaded?: Number; // when file was succesfully uploaded to Storage
  whenProcessed?: Number; // when file was processed into thumbnail version
  // values created by SeaFileUpload component
  src?: string; // what can be passed to an img tag
  base64?: string; // image data that can be used to construct an img.src (see getFileImageSrc). Can also be uploaded as data
  sfDocJson?: string; // json data for sfdoc files (only used for native)
  name?: string; // original file name (not present when using getPhoto)
  contentType?: string; // mime type that was in original file (not present when using getPhoto)
  lastModified?: number; // last modified for the original file (not present when using getPhoto)
  authorId?: string; // userId for the author who created or edited the document (only used for *.sfdocs)
  unique?: string; // temporary unique identifier for use by key
  isSignature?: boolean; // set to true if it's a signature
  emailToken?: string; // email passcode to allow emails to access file later on (uuid v4)
}

export const mimeTypes = {
  "3g2": "video/3gpp2",
  "3gp": "video/3gpp",
  "7z": "application/x-7z-compressed",
  aac: "audio/aac",
  avi: "video/x-msvideo",
  azw: "application/vnd.amazon.ebook",
  bin: "application/octet-stream",
  bmp: "image/bmp",
  bz: "application/x-bzip",
  bz2: "application/x-bzip2",
  csv: "text/csv",
  doc: "application/msword",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  epub: "application/epub+zip",
  gif: "image/gif",
  gz: "application/gzip",
  // heic: 'image/heic', // These currently dont convert due to firebase imagemagick being v6 (my guess anyway)
  // heif: 'image/heif',
  htm: "text/html",
  html: "text/html",
  jar: "application/java-archive",
  jpeg: "image/jpeg",
  jpg: "image/jpeg",
  js: "text/javascript",
  json: "application/json",
  jsonld: "application/ld+json",
  mid: "audio/midi",
  midi: "audio/midi",
  mp3: "audio/mpeg",
  mp4: "video/mp4",
  mpeg: "video/mpeg",
  odp: "application/vnd.oasis.opendocument.presentation",
  ods: "application/vnd.oasis.opendocument.spreadsheet",
  odt: "application/vnd.oasis.opendocument.text",
  oga: "audio/ogg",
  ogv: "video/ogg",
  ogx: "application/ogg",
  opus: "audio/opus",
  pdf: "application/pdf",
  png: "image/png",
  ppt: "application/vnd.ms-powerpoint",
  pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  rar: "application/vnd.rar",
  rtf: "application/rtf",
  svg: "image/svg+xml",
  tar: "application/x-tar",
  tif: "image/tiff",
  tiff: "image/tiff",
  ts: "video/mp2t",
  txt: "text/plain",
  vsd: "application/vnd.visio",
  wav: "audio/wav",
  weba: "audio/webm",
  webm: "video/webm",
  webp: "image/webp",
  xhtml: "application/xhtml+xml",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  xml: "text/xml",
  zip: "application/zip",
} as any;
// Convert an array of SeaFile to an array of "<state><id>.<ext>" values
export const seaFilesToValue = (files: SeaFile[]): string[] => {
  const array = [] as string[];
  files.forEach((file: SeaFile) => {
    array.push(
      `${file?.state}${file.id}${file.name ? "_" + file.name.substring(0, file.name.lastIndexOf(".")) : ""}.${file.ext}`,
    );
  });
  return array;
};
export const signatureToValue = (
  file: SeaFile | undefined,
): string | undefined => {
  if (file) {
    return `${file.state}${file.id}.${file.ext}`;
  }
  return undefined;
};

export const sfdocToValue = (file: SeaFile): string | undefined => {
  if (file) {
    // return `${file.state}${file.id}_${file.name}.sfdoc`; // For now, we don't have a good reason to embed the title... its repeated redundantly and expected to be already known. Will not be visible with the fullscreen viewer anyway for the foreseeable future
    return `${file.state}${file.id}^${file.authorId}.sfdoc`; // authorId is embedded because sfdocs are uniquely created/edited by users directly
  }
  return undefined;
};

export const makeSfdocSeaFile = (
  json: string,
  collection: string,
  name: string,
  authorId: string,
) => {
  return {
    collection: collection,
    field: "sfdoc",
    ext: "sfdoc",
    contentType: "application/seaflux",
    lastModified: Date.now(),
    // base64: base64, // binary to ascii. use atob() to get original string back
    sfDocJson: json,
    name: `${name}.sfdoc`,
    authorId: authorId,
  } as SeaFile;
};
