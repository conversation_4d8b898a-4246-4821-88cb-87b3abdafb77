import {
  ISafetyCheckService,
  SafetyCheckService,
} from "@src/domain/services/SafetyCheckService";
import { inject, injectable } from "inversify";
import {
  ActionLogService,
  IActionLogService,
} from "@src/domain/services/ActionLogService";
import { IUseCase } from "@src/domain/use-cases/UseCase";
import { SERVICES } from "@src/domain/di/ServiceRegistry";
import { IFirestoreService } from "@src/domain/data/IFirestoreService";
import { ILogger } from "@src/domain/util/ILogger";
import { FileService } from "@src/domain/services/FileService";
import { SeaFile } from "@src/lib/fileImports";

export interface CreateSafetyCheckDto {
  vesselId: string;
  categoryId: string;
  locationId: string;
  itemId: string;
  isCritical: boolean;
  whenLastChecked: number;
  description: string;
  files: SeaFile[];
  interval: string;
  assignedTo: string[];
  estimatedTime?: number;
  // TODO - Add links
}

export interface ICreateSafetyCheckUseCase
  extends IUseCase<CreateSafetyCheckDto> {}

@injectable()
export class CreateSafetyCheckUseCase implements ICreateSafetyCheckUseCase {
  private readonly logger: ILogger;
  constructor(
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService,
  ) {
    this.logger = logger.scoped("CreateSafetyCheckUseCase");
  }

  // TODO
  // - Handle creation of new categories (?)
  // - Handle links
  public async execute(
    createSafetyCheckDto: CreateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ) {
    this.logger.info("Executing CreateSafetyCheck Use Case");

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      createSafetyCheckDto.files,
      "safetyCheckItems",
      "files",
      userId,
      licenseeId,
    );
    createSafetyCheckDto.files = filesList as SeaFile[];

    // Create Safety Check
    const operation = this.firestoreService.createOperation({
      operationType: "create",
      operationDescription: "Create Safety Check",
      maximumBatchSize: 20,
    });

    const { ref: createdSafetyCheckRef, records: safetyCheckRecords } =
      this.safetyCheckService.createSafetyCheck(
        operation,
        createSafetyCheckDto,
        userId,
        licenseeId,
      );

    this.logger.debug("Safety Check Records", { safetyCheckRecords });

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      createSafetyCheckDto.vesselId,
      "safetyCheckItems",
      createdSafetyCheckRef.id,
      "TODO - Details", // TODO - This needs to be the name e.g. EPIRB, Life Raft
    );

    operation.addMany(safetyCheckRecords).add(actionLogRecord);

    await operation.commit();
  }
}
