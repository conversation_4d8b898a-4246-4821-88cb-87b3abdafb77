import { IUseCase } from "@src/domain/use-cases/UseCase";
import { inject, injectable } from "inversify";
import {
  ISafetyCheckService,
  SafetyCheckService,
} from "@src/domain/services/SafetyCheckService";
import { ActionLogService } from "@src/domain/services/ActionLogService";
import { SERVICES } from "@src/domain/di/ServiceRegistry";
import { ILogger } from "@src/domain/util/ILogger";
import { IFirestoreService } from "@src/domain/data/IFirestoreService";
import { FileService } from "@src/domain/services/FileService";
import { SeaFile } from "@src/lib/fileImports";

export interface UpdateSafetyCheckDto {
  vesselId: string;
  safetyCheckId: string;
  itemId: string;
  isCritical: boolean;
  locationId: string;
  categoryId: string;
  description: string;
  files: SeaFile[];
  interval: string;
  assignedTo: string[];
  whenLastChecked: number;
  estimatedTime: number;
  // links: string[];
}

export interface IUpdateSafetyCheckUseCase
  extends IUseCase<UpdateSafetyCheckDto> {}

@injectable()
export class UpdateSafetyCheckUseCase implements IUpdateSafetyCheckUseCase {
  private readonly logger: ILogger;

  constructor(
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService,
  ) {
    this.logger = logger.scoped("CreateSafetyEquipmentExpiryUseCase");
  }

  public async execute(
    dto: UpdateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      "safetyCheckItems",
      "files",
      userId,
      licenseeId,
    );
    dto.files = filesList as SeaFile[];

    // Update Safety Check
    const operation = this.firestoreService.createOperation({
      operationType: "update",
      operationDescription: "Update Safety Check",
      maximumBatchSize: 20,
    });

    const { ref: updatedSafetyCheckRef, records: safetyCheckRecords } =
      this.safetyCheckService.updateSafetyCheck(
        operation,
        dto,
        userId,
        licenseeId,
      );

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      "safetyCheckItems",
      updatedSafetyCheckRef.id,
      "TODO - Details", // This needs to be the name e.g. EPIRB, Life Raft
    );

    operation.addMany(safetyCheckRecords).add(actionLogRecord);

    await operation.commit();
  }
}
