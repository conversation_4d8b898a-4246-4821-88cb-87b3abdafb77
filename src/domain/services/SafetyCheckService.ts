import {
  deleteValue,
  FirestoreOperation,
  FirestoreRecord,
} from "@src/domain/data/FirestoreOperation";
import { serverTimestamp } from "@src/lib/firebase/services/firestore.service";
import { CreateSafetyCheckDto } from "@src/domain/use-cases/safety/CreateSafetyCheckUseCase";
import { inject, injectable } from "inversify";
import { UpdateSafetyCheckDto } from "@src/domain/use-cases/safety/UpdateSafetyCheckUseCase";
import { DocRef } from "@src/domain/data/IFirestoreService";
import { addInterval, hours24ToMillis } from "@src/lib/datesAndTime";
import { SERVICES } from "@src/domain/di/ServiceRegistry";
import { ILogger } from "@src/domain/util/ILogger";
import { DateTime } from "luxon";
import { CompleteSafetyCheckDto } from "@src/domain/use-cases/safety/CompleteSafetyCheckUseCase";
import { seaFilesToValue } from "@src/lib/fileImports";

export interface ISafetyCheckService {
  createSafetyCheck(
    operation: FirestoreOperation,
    createSafetyCheckDto: CreateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  updateSafetyCheck(
    operation: FirestoreOperation,
    updateSafetyCheckDto: UpdateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };

  completeSafetyCheck(
    operation: FirestoreOperation,
    updateSafetyCheckDto: CompleteSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ): {
    ref: DocRef;
    records: FirestoreRecord[];
  };
}

@injectable()
export class SafetyCheckService implements ISafetyCheckService {
  private readonly logger: ILogger;
  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped("SafetyCheckService");
  }

  createSafetyCheck(
    operation: FirestoreOperation,
    dto: CreateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ) {
    const dateDue = this.calculateDateDue(dto.whenLastChecked, dto.interval);
    console.debug("DTO", { dto });
    const newSafetyCheckRecord = {
      ref: operation.makeRef("safetyCheckItems"),
      data: {
        vesselId: dto.vesselId,
        addedBy: userId,
        itemId: dto.itemId,
        locationId: dto.locationId ?? undefined,
        categoryId: dto.categoryId ?? undefined,
        description: dto.description ?? undefined,
        interval: dto.interval,
        hasFault: false,
        whenLastChecked: dto.whenLastChecked,
        dateDue: dateDue,
        whenAdded: Date.now(), // TODO - Use batchTrace.whenAdded?
        state: "active",
        files: dto.files,
        assignedTo: dto.assignedTo ?? [],
        touched: serverTimestamp(),
        estimatedTime: dto.estimatedTime ?? undefined,
      },
    };

    const vesselSafetyItemRecord = {
      ref: operation.makeRef("vesselSafetyItems", dto.itemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    };

    const whenVesselTouchedRecord: FirestoreRecord =
      this.getWhenVesselTouchedRecord(operation, dto.vesselId, licenseeId);

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(
      operation,
      licenseeId,
      dto.vesselId,
    );

    this.logger.debug("Created records for new Safety Check: ", {
      newSafetyCheckRecord,
      whenVesselTouchedRecord,
      overdueStatsRecord,
    });

    return {
      ref: newSafetyCheckRecord.ref,
      records: [
        newSafetyCheckRecord,
        vesselSafetyItemRecord,
        whenVesselTouchedRecord,
        overdueStatsRecord,
      ],
    };
  }

  private calculateDateDue(whenLastChecked: number, interval: string): string {
    const dateDue = addInterval(whenLastChecked, interval).toISODate();
    if (!dateDue) {
      throw new Error(
        `Unable to calculate DateDue from date and interval: ${whenLastChecked} and ${interval}`,
      );
    }

    return dateDue;
  }

  public updateSafetyCheck(
    operation: FirestoreOperation,
    dto: UpdateSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ) {
    const updatedSafetyCheckRecord = {
      ref: operation.makeRef("safetyCheckItems", dto.safetyCheckId),
      data: {
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        itemId: dto.itemId,
        locationId: dto.locationId,
        categoryId: dto.categoryId,
        description: dto.description ?? deleteValue,
        interval: dto.interval,
        hasFault: false,
        whenLastChecked: dto.whenLastChecked,
        dateDue: addInterval(dto.whenLastChecked, dto.interval).toISODate(),
        assignedTo: dto.assignedTo,
        files: dto.files,
        touched: operation.serverTimestamp(),
        estimatedTime: dto.estimatedTime,
      },
      options: { merge: true },
    };

    const vesselSafetyItemRecord = {
      ref: operation.makeRef("vesselSafetyItems", dto.itemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    };

    const whenVesselTouchedRecord: FirestoreRecord =
      this.getWhenVesselTouchedRecord(operation, dto.vesselId, licenseeId);

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(
      operation,
      licenseeId,
      dto.vesselId,
    );

    return {
      ref: updatedSafetyCheckRecord.ref,
      records: [
        updatedSafetyCheckRecord,
        vesselSafetyItemRecord,
        whenVesselTouchedRecord,
        overdueStatsRecord,
      ],
    };
  }

  public completeSafetyCheck(
    operation: FirestoreOperation,
    dto: CompleteSafetyCheckDto,
    userId: string,
    licenseeId: string,
  ) {
    const completedSafetyCheckRecord = {
      ref: operation.makeRef("safetyCheckCompleted"),
      data: {
        safetyCheckId: dto.safetyCheckId,
        completedBy: userId,
        vesselId: dto.vesselId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        addedBy: userId,
        notes: dto.notes ?? undefined,
        whenCompleted: dto.whenCompleted,
        shouldReportFault: dto.shouldReportFault,
        files: dto.files,
        state: "active",
        touched: operation.serverTimestamp(),
        actualTime: dto.actualTime ?? undefined,
      },
    };

    const updatedSafetyCheckRecord = {
      ref: operation.makeRef("safetyCheckItems", dto.safetyCheckId),
      data: {
        whenLastChecked: dto.whenCompleted,
        dateDue: addInterval(dto.whenCompleted, dto.interval).toISODate(),
        hasFault: dto.shouldReportFault,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    };

    const whenVesselTouchedRecord: FirestoreRecord =
      this.getWhenVesselTouchedRecord(operation, dto.vesselId, licenseeId);

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(
      operation,
      licenseeId,
      dto.vesselId,
    );

    return {
      ref: completedSafetyCheckRecord.ref,
      records: [
        completedSafetyCheckRecord,
        updatedSafetyCheckRecord,
        whenVesselTouchedRecord,
        overdueStatsRecord,
      ],
    };
  }

  private getOverdueStatsRecord(
    operation: FirestoreOperation,
    licenseeId: string,
    vesselId: string,
  ) {
    return {
      ref: operation.makeRef("overdueStats", licenseeId),
      data: {
        [vesselId]: {
          safetyCheckItems: {
            stale: true,
          },
          vesselSafetyItems: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    };
  }

  private getWhenVesselTouchedRecord(
    operation: FirestoreOperation,
    vesselId: string,
    licenseeId: string,
  ) {
    return {
      ref: operation.makeRef("whenVesselTouched", vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyCheckItems: operation.serverTimestamp(),
        vesselSafetyItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    };
  }
}
