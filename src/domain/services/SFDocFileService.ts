import { inject, injectable } from "inversify";
import { FileService } from "@src/domain/services/FileService";
import { SeaFile, sfdocToValue } from "@src/lib/fileImports";
import { isNative, isWeb } from "@src/lib/device";
import { sharedState } from "@src/shared-state/shared-state";
import { SFDocNew } from "@src/domain/use-cases/companyDocumentRegister/CreateCompanyPlanUseCase";
import { SERVICES } from "@src/domain/di/ServiceRegistry";
import { IFirestoreService } from "@src/domain/data/IFirestoreService";
import { ILogger } from "@src/domain/util/ILogger";
import { fromByteArray } from "base64-js";

@injectable()
export class SFDocFileService extends FileService {
  protected readonly logger: ILogger;

  constructor(
    @inject(SERVICES.IFirestoreService)
    protected readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
  ) {
    super(firestoreService, logger);
    this.logger = logger.scoped("SFDocFileService");
  }

  /**
   * This is the main entry point function to upload SFDoc File to Firebase
   *
   * @param file
   * @param collection - The collection to upload the SFDoc file to
   * @param field - The field to upload the files to which will ideally be `files`
   * @param userId - The user id of the user uploading the files
   * @param licenseeId - The licensee id of the licensee uploading the files
   */
  async uploadSFDocFile(
    file: SeaFile,
    collection: string,
    field = "sfdoc",
    userId: string,
    licenseeId?: string,
  ): Promise<SFDocNew | undefined> {
    if (!file) return undefined;

    /** Step 1: Filter out files that must be uploaded and that are already uploaded */
    /** Step 1.1: Transform File */
    let filesToUpload = [this.getTransformedFile(file)];

    /** Step 2: Make Firebase request */
    // Execute the file data creation and get back the Firebase ref for the files
    filesToUpload = await this.executeFileDataCreation(
      filesToUpload,
      collection,
      field,
      userId,
      licenseeId,
    );

    /** Step 3: Store Files Locally & Upload */
    // Has 2 parts

    /** Step 3.1: Store Files Locally & Upload - Native */
    if (!isWeb && sharedState.licenseeSettings.current?.hasOffline) {
      const result = await this.nativeSequentialFileUpload(filesToUpload, []);

      if (result && result.length > 0) {
        return { [Date.now()]: sfdocToValue(result[0]) };
      }
      return undefined;
    }

    /** Step 3.2: Store Files Locally & Upload - Web */
    const result = await this.webSequentialFileUpload(
      filesToUpload,
      [],
      licenseeId,
    );
    if (result && result.length > 0) {
      return { [Date.now()]: sfdocToValue(result[0]) };
    }
    return undefined;
  }

  /**
   * Transform the SFDoc file to a format that can be stored locally and sent to Firebase
   * BEWARE: The `readFile` & `upload` logic also transform the files
   * TODO: Consolidate this logic
   *
   * @param _f - SeaFile
   * @returns Transformed SeaFile
   */
  protected getTransformedFile = (_f: SeaFile): SeaFile => {
    if (isWeb) {
      const fileData = _f.sfDocJson;
      const encoder = new TextEncoder();
      const utf8Bytes = encoder.encode(JSON.stringify(fileData));
      const base64 = fromByteArray(utf8Bytes);

      return {
        ..._f,
        base64: base64,
      };
    } else if (isNative) {
      // For Native - Store the Json string instead
      return {
        ..._f,
        base64: JSON.stringify(_f.sfDocJson),
      };
    }
    return _f;
  };
}
